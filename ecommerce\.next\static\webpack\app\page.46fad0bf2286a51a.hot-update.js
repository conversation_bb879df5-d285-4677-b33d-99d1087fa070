"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ScrollToTop */ \"(app-pages-browser)/./components/ui/ScrollToTop.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _styles_scroll_animations_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/scroll-animations.css */ \"(app-pages-browser)/./styles/scroll-animations.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Scroll animation setup\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    // Smooth spring animation for scroll progress\n    const smoothProgress = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring)(scrollYProgress, {\n        stiffness: 100,\n        damping: 30,\n        restDelta: 0.001\n    });\n    // Parallax transforms for background elements\n    const backgroundY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"50%\"\n    ]);\n    const backgroundScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        1\n    ], [\n        1,\n        1.1\n    ]);\n    const backgroundOpacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        0.5,\n        1\n    ], [\n        0.7,\n        0.4,\n        0.1\n    ]);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"bg-theme-homepage w-full relative overflow-hidden scroll-snap-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                    className: \"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary z-50 origin-left\",\n                    style: {\n                        scaleX: smoothProgress\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"absolute top-0 left-0 w-full h-[800px] bg-gradient-to-b from-theme-accent-primary/5 to-transparent\",\n                            style: {\n                                opacity: backgroundOpacity,\n                                scale: backgroundScale\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\",\n                            style: {\n                                y: backgroundY,\n                                scale: backgroundScale,\n                                opacity: backgroundOpacity\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/5 to-theme-accent-secondary/10 blur-3xl\",\n                            style: {\n                                y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n                                    0,\n                                    1\n                                ], [\n                                    \"0%\",\n                                    \"-30%\"\n                                ]),\n                                scale: backgroundScale,\n                                opacity: backgroundOpacity\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\",\n                            style: {\n                                y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n                                    0,\n                                    1\n                                ], [\n                                    \"0%\",\n                                    \"20%\"\n                                ]),\n                                scale: backgroundScale,\n                                opacity: backgroundOpacity\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10 scroll-snap-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"relative z-20\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                ease: \"easeOut\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                categories: categories !== null && categories !== void 0 ? categories : [],\n                                variant: \"navigation\",\n                                showTitle: false,\n                                showViewAll: false,\n                                maxCategories: 12,\n                                accentColor: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.section, {\n                                        className: \"relative w-full scroll-snap-section\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\",\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                className: \"relative z-10 mt-6 mb-8\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 30\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    ease: \"easeOut\",\n                                                    delay: 0.5\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        className: \"scroll-snap-section\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Featured Products\",\n                                            subtitle: \"Discover our handpicked selection of premium products\",\n                                            products: featuredProducts,\n                                            loading: futureProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"primary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\",\n                                            delay: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Discover Products\",\n                                            subtitle: \"Explore our most popular items\",\n                                            products: popularProducts,\n                                            loading: popularProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"secondary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-50px\"\n                                        },\n                                        transition: {\n                                            duration: 0.7,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                            title: \"Shop by Category\",\n                                            subtitle: \"Browse our collection by category\",\n                                            categories: categories || [],\n                                            accentColor: \"tertiary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 80\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.9,\n                                            ease: \"easeOut\",\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                            title: \"Browse Products by Category\",\n                                            subtitle: \"Filter products by your favorite categories\",\n                                            categories: categories || [],\n                                            categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                            accentColor: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: categoryIndex % 2 === 0 ? -60 : 60\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            viewport: {\n                                                once: true,\n                                                margin: \"-80px\"\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                ease: \"easeOut\",\n                                                delay: categoryIndex * 0.1\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: categoryData.category.name,\n                                                products: categoryData.products,\n                                                loading: categoryData.loading,\n                                                viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                                accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 480,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"7wu/4G25GfTzzSRwjEWH8r4JQJU=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});