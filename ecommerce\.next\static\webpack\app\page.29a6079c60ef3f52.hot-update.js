"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ScrollToTop */ \"(app-pages-browser)/./components/ui/ScrollToTop.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _styles_scroll_animations_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/scroll-animations.css */ \"(app-pages-browser)/./styles/scroll-animations.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Client-side only state for animations\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scroll animation setup - only on client\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    // Smooth spring animation for scroll progress\n    const smoothProgress = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring)(scrollYProgress, {\n        stiffness: 100,\n        damping: 30,\n        restDelta: 0.001\n    });\n    // Parallax transforms for background elements\n    const backgroundY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"50%\"\n    ]);\n    const backgroundScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        1\n    ], [\n        1,\n        1.1\n    ]);\n    const backgroundOpacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        0.5,\n        1\n    ], [\n        0.7,\n        0.4,\n        0.1\n    ]);\n    // Set client-side flag after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"Homepage.useEffect\"], []);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"bg-theme-homepage w-full relative overflow-hidden scroll-snap-container\",\n        children: [\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                className: \"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary z-50 origin-left\",\n                style: {\n                    scaleX: smoothProgress\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-0 left-0 w-full h-[800px] bg-gradient-to-b from-theme-accent-primary/5 to-transparent\",\n                        style: {\n                            opacity: backgroundOpacity,\n                            scale: backgroundScale\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\",\n                        style: {\n                            y: backgroundY,\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/5 to-theme-accent-secondary/10 blur-3xl\",\n                        style: {\n                            y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n                                0,\n                                1\n                            ], [\n                                \"0%\",\n                                \"-30%\"\n                            ]),\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\",\n                        style: {\n                            y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n                                0,\n                                1\n                            ], [\n                                \"0%\",\n                                \"20%\"\n                            ]),\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10 scroll-snap-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"relative z-20\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                ease: \"easeOut\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                categories: categories !== null && categories !== void 0 ? categories : [],\n                                variant: \"navigation\",\n                                showTitle: false,\n                                showViewAll: false,\n                                maxCategories: 12,\n                                accentColor: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.section, {\n                                        className: \"relative w-full scroll-snap-section\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\",\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                className: \"relative z-10 mt-6 mb-8\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 30\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    ease: \"easeOut\",\n                                                    delay: 0.5\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        className: \"scroll-snap-section\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Featured Products\",\n                                            subtitle: \"Discover our handpicked selection of premium products\",\n                                            products: featuredProducts,\n                                            loading: futureProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"primary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\",\n                                            delay: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Discover Products\",\n                                            subtitle: \"Explore our most popular items\",\n                                            products: popularProducts,\n                                            loading: popularProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"secondary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-50px\"\n                                        },\n                                        transition: {\n                                            duration: 0.7,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                            title: \"Shop by Category\",\n                                            subtitle: \"Browse our collection by category\",\n                                            categories: categories || [],\n                                            accentColor: \"tertiary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 80\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.9,\n                                            ease: \"easeOut\",\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                            title: \"Browse Products by Category\",\n                                            subtitle: \"Filter products by your favorite categories\",\n                                            categories: categories || [],\n                                            categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                            accentColor: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: categoryIndex % 2 === 0 ? -60 : 60\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            viewport: {\n                                                once: true,\n                                                margin: \"-80px\"\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                ease: \"easeOut\",\n                                                delay: categoryIndex * 0.1\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: categoryData.category.name,\n                                                products: categoryData.products,\n                                                loading: categoryData.loading,\n                                                viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                                accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 488,\n                columnNumber: 20\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"vt7coTIWIh+kmHDA7U0b4Lii9WE=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});