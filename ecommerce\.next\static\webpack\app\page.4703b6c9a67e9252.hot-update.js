"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ClientOnly.tsx":
/*!***********************************!*\
  !*** ./components/ClientOnly.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientOnly)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n/**\r\n * ClientOnly component ensures that children are only rendered on the client side\r\n * This prevents hydration errors when accessing browser-only APIs like localStorage\r\n */ function ClientOnly(param) {\n    let { children, fallback = null, className = \"\" } = param;\n    _s();\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientOnly.useEffect\": ()=>{\n            setHasMounted(true);\n        }\n    }[\"ClientOnly.useEffect\"], []);\n    if (!hasMounted) {\n        return fallback ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            children: fallback\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ClientOnly.tsx\",\n            lineNumber: 27,\n            columnNumber: 23\n        }, this) : null;\n    }\n    return className ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ClientOnly.tsx\",\n        lineNumber: 30,\n        columnNumber: 22\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ClientOnly, \"aiSd/DQPOnbbLLZZL0Xv/KtPBDg=\");\n_c = ClientOnly;\nvar _c;\n$RefreshReg$(_c, \"ClientOnly\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ClientOnly.tsx\n"));

/***/ })

});