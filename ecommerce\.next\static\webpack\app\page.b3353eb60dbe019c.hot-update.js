"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ScrollToTop */ \"(app-pages-browser)/./components/ui/ScrollToTop.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _styles_scroll_animations_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/scroll-animations.css */ \"(app-pages-browser)/./styles/scroll-animations.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Client-side only state for animations\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scroll animation setup - only on client\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    // Smooth spring animation for scroll progress\n    const smoothProgress = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring)(scrollYProgress, {\n        stiffness: 100,\n        damping: 30,\n        restDelta: 0.001\n    });\n    // Parallax transforms for background elements\n    const backgroundY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"50%\"\n    ]);\n    const backgroundScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        1\n    ], [\n        1,\n        1.1\n    ]);\n    const backgroundOpacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        0.5,\n        1\n    ], [\n        0.7,\n        0.4,\n        0.1\n    ]);\n    // Set client-side flag after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"Homepage.useEffect\"], []);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"bg-theme-homepage w-full relative overflow-hidden scroll-snap-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                className: \"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary z-50 origin-left\",\n                style: {\n                    scaleX: smoothProgress\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-0 left-0 w-full h-[800px] bg-gradient-to-b from-theme-accent-primary/5 to-transparent\",\n                        style: {\n                            opacity: backgroundOpacity,\n                            scale: backgroundScale\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\",\n                        style: {\n                            y: backgroundY,\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/5 to-theme-accent-secondary/10 blur-3xl\",\n                        style: {\n                            y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n                                0,\n                                1\n                            ], [\n                                \"0%\",\n                                \"-30%\"\n                            ]),\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\",\n                        style: {\n                            y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n                                0,\n                                1\n                            ], [\n                                \"0%\",\n                                \"20%\"\n                            ]),\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10 scroll-snap-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"relative z-20\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                ease: \"easeOut\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                categories: categories !== null && categories !== void 0 ? categories : [],\n                                variant: \"navigation\",\n                                showTitle: false,\n                                showViewAll: false,\n                                maxCategories: 12,\n                                accentColor: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.section, {\n                                        className: \"relative w-full scroll-snap-section\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\",\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                className: \"relative z-10 mt-6 mb-8\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 30\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    ease: \"easeOut\",\n                                                    delay: 0.5\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        className: \"scroll-snap-section\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Featured Products\",\n                                            subtitle: \"Discover our handpicked selection of premium products\",\n                                            products: featuredProducts,\n                                            loading: futureProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"primary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\",\n                                            delay: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Discover Products\",\n                                            subtitle: \"Explore our most popular items\",\n                                            products: popularProducts,\n                                            loading: popularProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"secondary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-50px\"\n                                        },\n                                        transition: {\n                                            duration: 0.7,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                            title: \"Shop by Category\",\n                                            subtitle: \"Browse our collection by category\",\n                                            categories: categories || [],\n                                            accentColor: \"tertiary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 80\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.9,\n                                            ease: \"easeOut\",\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                            title: \"Browse Products by Category\",\n                                            subtitle: \"Filter products by your favorite categories\",\n                                            categories: categories || [],\n                                            categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                            accentColor: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: categoryIndex % 2 === 0 ? -60 : 60\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            viewport: {\n                                                once: true,\n                                                margin: \"-80px\"\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                ease: \"easeOut\",\n                                                delay: categoryIndex * 0.1\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: categoryData.category.name,\n                                                products: categoryData.products,\n                                                loading: categoryData.loading,\n                                                viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                                accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"vt7coTIWIh+kmHDA7U0b4Lii9WE=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});