"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ScrollToTop */ \"(app-pages-browser)/./components/ui/ScrollToTop.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _styles_scroll_animations_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/scroll-animations.css */ \"(app-pages-browser)/./styles/scroll-animations.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Client-side only state for animations\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scroll animation setup - only on client\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    // Smooth spring animation for scroll progress\n    const smoothProgress = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring)(scrollYProgress, {\n        stiffness: 100,\n        damping: 30,\n        restDelta: 0.001\n    });\n    // Parallax transforms for background elements\n    const backgroundY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"50%\"\n    ]);\n    const backgroundScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        1\n    ], [\n        1,\n        1.1\n    ]);\n    const backgroundOpacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n        0,\n        0.5,\n        1\n    ], [\n        0.7,\n        0.4,\n        0.1\n    ]);\n    // Set client-side flag after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"Homepage.useEffect\"], []);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"bg-theme-homepage w-full relative overflow-hidden scroll-snap-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                    className: \"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary z-50 origin-left\",\n                    style: {\n                        scaleX: smoothProgress\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-0 left-0 w-full h-[800px] bg-gradient-to-b from-theme-accent-primary/5 to-transparent\",\n                        style: {\n                            opacity: backgroundOpacity,\n                            scale: backgroundScale\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\",\n                        style: {\n                            y: backgroundY,\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/5 to-theme-accent-secondary/10 blur-3xl\",\n                        style: {\n                            y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n                                0,\n                                1\n                            ], [\n                                \"0%\",\n                                \"-30%\"\n                            ]),\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\",\n                        style: {\n                            y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(smoothProgress, [\n                                0,\n                                1\n                            ], [\n                                \"0%\",\n                                \"20%\"\n                            ]),\n                            scale: backgroundScale,\n                            opacity: backgroundOpacity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10 scroll-snap-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"relative z-20\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                ease: \"easeOut\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                categories: categories !== null && categories !== void 0 ? categories : [],\n                                variant: \"navigation\",\n                                showTitle: false,\n                                showViewAll: false,\n                                maxCategories: 12,\n                                accentColor: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.section, {\n                                        className: \"relative w-full scroll-snap-section\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\",\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                className: \"relative z-10 mt-6 mb-8\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 30\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    ease: \"easeOut\",\n                                                    delay: 0.5\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        className: \"scroll-snap-section\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Featured Products\",\n                                            subtitle: \"Discover our handpicked selection of premium products\",\n                                            products: featuredProducts,\n                                            loading: futureProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"primary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            ease: \"easeOut\",\n                                            delay: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Discover Products\",\n                                            subtitle: \"Explore our most popular items\",\n                                            products: popularProducts,\n                                            loading: popularProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"secondary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-50px\"\n                                        },\n                                        transition: {\n                                            duration: 0.7,\n                                            ease: \"easeOut\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                            title: \"Shop by Category\",\n                                            subtitle: \"Browse our collection by category\",\n                                            categories: categories || [],\n                                            accentColor: \"tertiary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 80\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true,\n                                            margin: \"-100px\"\n                                        },\n                                        transition: {\n                                            duration: 0.9,\n                                            ease: \"easeOut\",\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                            title: \"Browse Products by Category\",\n                                            subtitle: \"Filter products by your favorite categories\",\n                                            categories: categories || [],\n                                            categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                            accentColor: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: categoryIndex % 2 === 0 ? -60 : 60\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            viewport: {\n                                                once: true,\n                                                margin: \"-80px\"\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                ease: \"easeOut\",\n                                                delay: categoryIndex * 0.1\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: categoryData.category.name,\n                                                products: categoryData.products,\n                                                loading: categoryData.loading,\n                                                viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                                accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 488,\n                columnNumber: 20\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"vt7coTIWIh+kmHDA7U0b4Lii9WE=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDd0M7QUFDbUM7QUFDckI7QUFPNUI7QUFDVztBQUNHO0FBQ2tDO0FBQ1o7QUFDUjtBQUNMO0FBQ1I7QUFFekMseURBQXlEO0FBQ3pELE1BQU1xQiw2QkFBZXJCLGlEQUFVLENBQUMsSUFBTSxtUEFBeUM7S0FBekVxQjtBQUNOLE1BQU1FLCtCQUFpQnZCLGlEQUFVLENBQUMsSUFBTSxzUEFBMEM7TUFBNUV1QjtBQUNOLE1BQU1DLDZCQUFleEIsaURBQVUsQ0FBQyxJQUFNLGdQQUF3QztNQUF4RXdCO0FBQ04sTUFBTUMsa0NBQW9CekIsaURBQVUsQ0FBQyxJQUFNLCtQQUE2QztNQUFsRnlCO0FBUU4sTUFBTUMsV0FBVzs7SUFDZixNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHdEIsa0VBQVFBO0lBQzFCLDhDQUE4QztJQUM5QyxNQUFNLEVBQUV1QixJQUFJLEVBQUVDLElBQUksRUFBRSxHQUFRbEIseURBQU1BLENBQUNGLG9EQUFRQSxJQUFJO0lBRS9DLHVEQUF1RDtJQUN2RCxNQUFNLENBQUNxQixlQUFlQyxpQkFBaUIsR0FBR2hCLCtDQUFRQSxDQUFNO0lBQ3hELE1BQU0sQ0FBQ2lCLHNCQUFzQkMsd0JBQXdCLEdBQUdsQiwrQ0FBUUEsQ0FBVTtJQUMxRSxNQUFNLENBQUNtQixnQkFBZ0JDLGtCQUFrQixHQUFHcEIsK0NBQVFBLENBQU07SUFDMUQsTUFBTSxDQUFDcUIsdUJBQXVCQyx5QkFBeUIsR0FBR3RCLCtDQUFRQSxDQUFVO0lBQzVFLE1BQU0sQ0FBQ3VCLFlBQVlDLGNBQWMsR0FBR3hCLCtDQUFRQSxDQUFRLEVBQUU7SUFDdEQsTUFBTSxDQUFDeUIsbUJBQW1CQyxxQkFBcUIsR0FBRzFCLCtDQUFRQSxDQUFVO0lBRXBFLDZDQUE2QztJQUM3QyxNQUFNMkIsd0JBQXdCekIsNkNBQU1BLENBQVU7SUFFOUMsd0NBQXdDO0lBQ3hDLE1BQU0sQ0FBQzBCLFVBQVVDLFlBQVksR0FBRzdCLCtDQUFRQSxDQUFDO0lBRXpDLDBDQUEwQztJQUMxQyxNQUFNOEIsZUFBZTVCLDZDQUFNQSxDQUFpQjtJQUM1QyxNQUFNLEVBQUU2QixlQUFlLEVBQUUsR0FBRzVDLHlEQUFTQSxDQUFDO1FBQ3BDNkMsUUFBUUY7UUFDUkcsUUFBUTtZQUFDO1lBQWU7U0FBWTtJQUN0QztJQUVBLDhDQUE4QztJQUM5QyxNQUFNQyxpQkFBaUI3Qyx5REFBU0EsQ0FBQzBDLGlCQUFpQjtRQUNoREksV0FBVztRQUNYQyxTQUFTO1FBQ1RDLFdBQVc7SUFDYjtJQUVBLDhDQUE4QztJQUM5QyxNQUFNQyxjQUFjbEQsNERBQVlBLENBQUM4QyxnQkFBZ0I7UUFBQztRQUFHO0tBQUUsRUFBRTtRQUFDO1FBQU07S0FBTTtJQUN0RSxNQUFNSyxrQkFBa0JuRCw0REFBWUEsQ0FBQzhDLGdCQUFnQjtRQUFDO1FBQUc7S0FBRSxFQUFFO1FBQUM7UUFBRztLQUFJO0lBQ3JFLE1BQU1NLG9CQUFvQnBELDREQUFZQSxDQUFDOEMsZ0JBQWdCO1FBQUM7UUFBRztRQUFLO0tBQUUsRUFBRTtRQUFDO1FBQUs7UUFBSztLQUFJO0lBRW5GLHVDQUF1QztJQUN2Q25DLGdEQUFTQTs4QkFBQztZQUNSOEIsWUFBWTtRQUNkOzZCQUFHLEVBQUU7SUFFTCxNQUFNLENBQUNZLGtCQUFrQkMsb0JBQW9CLEdBQUcxQywrQ0FBUUEsQ0FBcUIsRUFBRTtJQUUvRSwrQ0FBK0M7SUFDL0MsTUFBTTJDLDZCQUE2QnpDLDZDQUFNQSxDQUFVO0lBRW5ELDJGQUEyRjtJQUMzRixNQUFNMEMsd0JBQXdCOUMsa0RBQVdBO3VEQUFDO1lBQ3hDLHVDQUF1QztZQUN2QyxJQUFJLENBQUMrQyxNQUFNQyxPQUFPLENBQUN2QixlQUFlQSxXQUFXd0IsTUFBTSxLQUFLLEdBQUc7WUFFM0QsOENBQThDO1lBQzlDLElBQUlKLDJCQUEyQkssT0FBTyxJQUNsQ1AsaUJBQWlCTSxNQUFNLEdBQUcsS0FDMUJOLGlCQUFpQlEsS0FBSzsrREFBQ0MsQ0FBQUEsTUFBTyxDQUFDQSxJQUFJQyxPQUFPOytEQUFHO2dCQUMvQztZQUNGO1lBRUEsa0ZBQWtGO1lBQ2xGLE1BQU1DLG9CQUFvQjdCLFdBQVc4QixLQUFLLENBQUMsR0FBRztZQUU5Qyw0QkFBNEI7WUFDNUIsSUFBSVosaUJBQWlCTSxNQUFNLEtBQUssR0FBRztnQkFDakMsTUFBTU8sMEJBQTBCRixrQkFBa0JHLEdBQUc7MkZBQUMsQ0FBQ0MsV0FBbUI7NEJBQ3hFQTs0QkFDQUMsVUFBVSxFQUFFOzRCQUNaTixTQUFTO3dCQUNYOztnQkFDQVQsb0JBQW9CWTtZQUN0QjtZQUVBLG1DQUFtQztZQUNuQyxNQUFNSSxXQUFXTixrQkFBa0JHLEdBQUc7d0VBQUMsT0FBT0MsVUFBVUc7b0JBQ3RELElBQUk7NEJBU1VDO3dCQVJaLGtFQUFrRTt3QkFDbEUsTUFBTUEsU0FBUyxNQUFNOUMsS0FDbkIsR0FBc0MsT0FBbkN0QixtRUFBbUJBLENBQUNnRSxTQUFTSyxJQUFJLEdBQUU7d0JBR3hDLE9BQU87NEJBQ0xGOzRCQUNBSDs0QkFDQUMsVUFBVUcsQ0FBQUEsbUJBQUFBLDhCQUFBQSxrQkFBQUEsT0FBUUUsT0FBTyxjQUFmRixzQ0FBQUEsZ0JBQWlCSCxRQUFRLEtBQUksRUFBRTs0QkFDekNNLFNBQVM7d0JBQ1g7b0JBQ0YsRUFBRSxPQUFPQyxPQUFPO3dCQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQTZDLE9BQWRSLFNBQVNVLElBQUksRUFBQyxNQUFJRjt3QkFDL0QsT0FBTzs0QkFDTEw7NEJBQ0FIOzRCQUNBQyxVQUFVLEVBQUU7NEJBQ1pNLFNBQVM7d0JBQ1g7b0JBQ0Y7Z0JBQ0Y7O1lBRUEsbUNBQW1DO1lBQ25DLE1BQU1ELFVBQVUsTUFBTUssUUFBUUMsR0FBRyxDQUFDVjtZQUVsQyxxQ0FBcUM7WUFDckNoQjsrREFBb0IyQixDQUFBQTtvQkFDbEIsMkNBQTJDO29CQUMzQyxNQUFNQyxXQUFXRCxLQUFLdEIsTUFBTSxHQUFHLElBQUk7MkJBQUlzQjtxQkFBSyxHQUMxQ2pCLGtCQUFrQkcsR0FBRzt1RUFBQ0wsQ0FBQUEsTUFBUTtnQ0FBRU0sVUFBVU47Z0NBQUtPLFVBQVUsRUFBRTtnQ0FBRU4sU0FBUzs0QkFBSzs7b0JBRTdFLDBCQUEwQjtvQkFDMUJXLFFBQVFTLE9BQU87dUVBQUNYLENBQUFBOzRCQUNkLElBQUlVLFFBQVEsQ0FBQ1YsT0FBT0QsS0FBSyxDQUFDLEVBQUU7Z0NBQzFCVyxRQUFRLENBQUNWLE9BQU9ELEtBQUssQ0FBQyxHQUFHO29DQUN2QixHQUFHVyxRQUFRLENBQUNWLE9BQU9ELEtBQUssQ0FBQztvQ0FDekJGLFVBQVVHLE9BQU9ILFFBQVE7b0NBQ3pCTixTQUFTO2dDQUNYOzRCQUNGO3dCQUNGOztvQkFFQSxrQkFBa0I7b0JBQ2xCUiwyQkFBMkJLLE9BQU8sR0FBRztvQkFFckMsT0FBT3NCO2dCQUNUOztRQUNGO3NEQUFHO1FBQUMvQztRQUFZVDtLQUFLO0lBRXJCLG1HQUFtRztJQUNuR2YsZ0RBQVNBOzhCQUFDO1lBQ1Isd0NBQXdDO1lBQ3hDLElBQUk0QixzQkFBc0JxQixPQUFPLEVBQUU7WUFFbkMsMkRBQTJEO1lBQzNELElBQUl3QixZQUFZO1lBRWhCLE1BQU1DO3NEQUFrQjtvQkFDdEIsSUFBSTt3QkFDRi9DLHFCQUFxQjt3QkFDckJSLHdCQUF3Qjt3QkFDeEJJLHlCQUF5Qjt3QkFFekIsd0JBQXdCO3dCQUN4QixNQUFNb0QsbUJBQW1CLE1BQU01RCxLQUFLdkIsc0RBQVVBO3dCQUU5Qyw4Q0FBOEM7d0JBQzlDLElBQUksQ0FBQ2lGLFdBQVc7d0JBRWhCLDBCQUEwQjt3QkFDMUIsSUFBSUUsa0JBQWtCOzRCQUNwQmxELGNBQWNrRDt3QkFDaEI7d0JBRUFoRCxxQkFBcUI7d0JBRXJCLGlEQUFpRDt3QkFDakQsTUFBTSxDQUFDaUQsZ0JBQWdCQyxjQUFjLEdBQUcsTUFBTVQsUUFBUUMsR0FBRyxDQUFDOzRCQUN4RHRELEtBQUtyQiw0REFBZ0JBOzRCQUNyQnFCLEtBQUtuQixvREFBUUEsR0FBRyxpQkFBaUIsNkNBQTZDO3lCQUMvRTt3QkFFRCxJQUFJLENBQUM2RSxXQUFXO3dCQUVoQixpQ0FBaUM7d0JBQ2pDLElBQUlHLGdCQUFnQjs0QkFDbEIzRCxpQkFBaUIyRDt3QkFDbkI7d0JBRUEsZ0NBQWdDO3dCQUNoQyxJQUFJQyxlQUFlOzRCQUNqQnhELGtCQUFrQndEO3dCQUNwQjt3QkFFQSxpREFBaUQ7d0JBQ2pEakQsc0JBQXNCcUIsT0FBTyxHQUFHO3dCQUNoQzlCLHdCQUF3Qjt3QkFDeEJJLHlCQUF5QjtvQkFDM0IsRUFBRSxPQUFPMEMsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7d0JBQzdDdEMscUJBQXFCO3dCQUNyQlIsd0JBQXdCO3dCQUN4QkkseUJBQXlCO29CQUMzQjtnQkFDRjs7WUFFQW1EO1lBRUEsMERBQTBEO1lBQzFEO3NDQUFPO29CQUNMRCxZQUFZO2dCQUNkOztRQUNGOzZCQUFHO1FBQUMxRDtLQUFLO0lBRVQsOERBQThEO0lBQzlELDBFQUEwRTtJQUMxRSwrREFBK0Q7SUFDL0RmLGdEQUFTQTs4QkFBQztZQUNSLElBQUl3QixjQUFjQSxXQUFXd0IsTUFBTSxHQUFHLEtBQUssQ0FBQ0osMkJBQTJCSyxPQUFPLEVBQUU7Z0JBQzlFSjtZQUNGO1FBQ0Y7NkJBQUc7UUFBQ0E7UUFBdUJyQjtLQUFXO0lBR3RDLDJFQUEyRTtJQUUzRSxpRkFBaUY7SUFDakYsTUFBTXNELG1CQUFtQjVFLDhDQUFPQTs4Q0FBQztZQUMvQixJQUFJYyxpQkFBaUI4QixNQUFNQyxPQUFPLENBQUMvQixnQkFBZ0I7Z0JBQ2pELE9BQU9BO1lBQ1QsT0FBTyxJQUFJQSxpQkFBaUJBLGNBQWMrQyxPQUFPLElBQUlqQixNQUFNQyxPQUFPLENBQUMvQixjQUFjK0MsT0FBTyxHQUFHO2dCQUN6RixPQUFPL0MsY0FBYytDLE9BQU87WUFDOUIsT0FBTyxJQUFJL0MsaUJBQWlCQSxjQUFjMEMsUUFBUSxJQUFJWixNQUFNQyxPQUFPLENBQUMvQixjQUFjMEMsUUFBUSxHQUFHO2dCQUMzRixPQUFPMUMsY0FBYzBDLFFBQVE7WUFDL0I7WUFDQSw0RUFBNEU7WUFDNUUsT0FBTyxFQUFFO1FBQ1g7NkNBQUc7UUFBQzFDO0tBQWM7SUFFbEIsNERBQTREO0lBQzVELE1BQU0rRCxrQkFBa0I3RSw4Q0FBT0E7NkNBQUM7WUFDOUIsaURBQWlEO1lBQ2pELElBQUlrQixrQkFBa0IwQixNQUFNQyxPQUFPLENBQUMzQixpQkFBaUI7Z0JBQ25ELE9BQU9BO1lBQ1QsT0FBTyxJQUFJQSxrQkFBa0JBLGVBQWUyQyxPQUFPLElBQUlqQixNQUFNQyxPQUFPLENBQUMzQixlQUFlMkMsT0FBTyxHQUFHO2dCQUM1RixPQUFPM0MsZUFBZTJDLE9BQU87WUFDL0IsT0FBTyxJQUFJM0Msa0JBQWtCQSxlQUFlc0MsUUFBUSxJQUFJWixNQUFNQyxPQUFPLENBQUMzQixlQUFlc0MsUUFBUSxHQUFHO2dCQUM5RixPQUFPdEMsZUFBZXNDLFFBQVE7WUFDaEM7WUFFQSw0RUFBNEU7WUFDNUUsSUFBSTVDLFFBQVFnQyxNQUFNQyxPQUFPLENBQUNqQyxPQUFPO2dCQUMvQixPQUFPQTtZQUNULE9BQU8sSUFBSUEsUUFBUUEsS0FBS2lELE9BQU8sSUFBSWpCLE1BQU1DLE9BQU8sQ0FBQ2pDLEtBQUtpRCxPQUFPLEdBQUc7Z0JBQzlELE9BQU9qRCxLQUFLaUQsT0FBTztZQUNyQixPQUFPLElBQUlqRCxRQUFRQSxLQUFLNEMsUUFBUSxJQUFJWixNQUFNQyxPQUFPLENBQUNqQyxLQUFLNEMsUUFBUSxHQUFHO2dCQUNoRSxPQUFPNUMsS0FBSzRDLFFBQVE7WUFDdEI7WUFFQSw0RUFBNEU7WUFDNUUsT0FBTyxFQUFFO1FBQ1g7NENBQUc7UUFBQ3RDO1FBQWdCTjtLQUFLO0lBRXpCLHFCQUNFLDhEQUFDa0U7UUFBSUMsS0FBS2xEO1FBQWNtRCxXQUFVOzswQkFFaEMsOERBQUM1RSw4REFBVUE7MEJBQ1QsNEVBQUNuQixrREFBTUEsQ0FBQzZGLEdBQUc7b0JBQ1RFLFdBQVU7b0JBQ1ZDLE9BQU87d0JBQUVDLFFBQVFqRDtvQkFBZTs7Ozs7Ozs7Ozs7WUFLbkNOLDBCQUNDLDhEQUFDbUQ7Z0JBQUlFLFdBQVU7O2tDQUNmLDhEQUFDL0Ysa0RBQU1BLENBQUM2RixHQUFHO3dCQUNURSxXQUFVO3dCQUNWQyxPQUFPOzRCQUNMRSxTQUFTNUM7NEJBQ1Q2QyxPQUFPOUM7d0JBQ1Q7Ozs7OztrQ0FFRiw4REFBQ3JELGtEQUFNQSxDQUFDNkYsR0FBRzt3QkFDVEUsV0FBVTt3QkFDVkMsT0FBTzs0QkFDTEksR0FBR2hEOzRCQUNIK0MsT0FBTzlDOzRCQUNQNkMsU0FBUzVDO3dCQUNYOzs7Ozs7a0NBRUYsOERBQUN0RCxrREFBTUEsQ0FBQzZGLEdBQUc7d0JBQ1RFLFdBQVU7d0JBQ1ZDLE9BQU87NEJBQ0xJLEdBQUdsRyw0REFBWUEsQ0FBQzhDLGdCQUFnQjtnQ0FBQztnQ0FBRzs2QkFBRSxFQUFFO2dDQUFDO2dDQUFNOzZCQUFPOzRCQUN0RG1ELE9BQU85Qzs0QkFDUDZDLFNBQVM1Qzt3QkFDWDs7Ozs7O2tDQUVGLDhEQUFDdEQsa0RBQU1BLENBQUM2RixHQUFHO3dCQUNURSxXQUFVO3dCQUNWQyxPQUFPOzRCQUNMSSxHQUFHbEcsNERBQVlBLENBQUM4QyxnQkFBZ0I7Z0NBQUM7Z0NBQUc7NkJBQUUsRUFBRTtnQ0FBQztnQ0FBTTs2QkFBTTs0QkFDckRtRCxPQUFPOUM7NEJBQ1A2QyxTQUFTNUM7d0JBQ1g7Ozs7Ozs7Ozs7OzswQkFNSiw4REFBQzNDLHVEQUFPQTswQkFDTiw0RUFBQ2tGO29CQUFJRSxXQUFVOztzQ0FFYiw4REFBQy9GLGtEQUFNQSxDQUFDNkYsR0FBRzs0QkFDVEUsV0FBVTs0QkFDVk0sU0FBUztnQ0FBRUgsU0FBUztnQ0FBR0UsR0FBRyxDQUFDOzRCQUFHOzRCQUM5QkUsU0FBUztnQ0FBRUosU0FBUztnQ0FBR0UsR0FBRzs0QkFBRTs0QkFDNUJHLFlBQVk7Z0NBQUVDLFVBQVU7Z0NBQUtDLE1BQU07NEJBQVU7c0NBRTdDLDRFQUFDakY7Z0NBQ0NhLFlBQVlBLHVCQUFBQSx3QkFBQUEsYUFBYyxFQUFFO2dDQUM1QnFFLFNBQVE7Z0NBQ1JDLFdBQVc7Z0NBQ1hDLGFBQWE7Z0NBQ2JDLGVBQWU7Z0NBQ2ZDLGFBQVk7Ozs7Ozs7Ozs7O3NDQUtoQiw4REFBQzNGLDhEQUFVQTtzQ0FDVCw0RUFBQ3BCLHVEQUFjO2dDQUFDaUgsd0JBQ2QsOERBQUNuQjtvQ0FBSUUsV0FBVTs7c0RBQ2IsOERBQUNGOzRDQUFJRSxXQUFVOzs7Ozs7c0RBQ2YsOERBQUNGOzRDQUFJRSxXQUFVOzs7Ozs7c0RBQ2YsOERBQUNGOzRDQUFJRSxXQUFVOzs7Ozs7Ozs7Ozs7O2tEQUlqQiw4REFBQy9GLGtEQUFNQSxDQUFDaUgsT0FBTzt3Q0FDYmxCLFdBQVU7d0NBQ1ZNLFNBQVM7NENBQUVILFNBQVM7NENBQUdDLE9BQU87d0NBQUs7d0NBQ25DRyxTQUFTOzRDQUFFSixTQUFTOzRDQUFHQyxPQUFPO3dDQUFFO3dDQUNoQ0ksWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBS0MsTUFBTTs0Q0FBV1MsT0FBTzt3Q0FBSTs7MERBRXpELDhEQUFDOUY7Ozs7OzBEQUNELDhEQUFDcEIsa0RBQU1BLENBQUM2RixHQUFHO2dEQUNURSxXQUFVO2dEQUNWTSxTQUFTO29EQUFFSCxTQUFTO29EQUFHRSxHQUFHO2dEQUFHO2dEQUM3QkUsU0FBUztvREFBRUosU0FBUztvREFBR0UsR0FBRztnREFBRTtnREFDNUJHLFlBQVk7b0RBQUVDLFVBQVU7b0RBQUtDLE1BQU07b0RBQVdTLE9BQU87Z0RBQUk7MERBRXpELDRFQUFDakcsc0VBQWVBOzs7Ozs7Ozs7Ozs7Ozs7O2tEQUt0Qiw4REFBQ2pCLGtEQUFNQSxDQUFDNkYsR0FBRzt3Q0FDVEUsV0FBVTt3Q0FDVk0sU0FBUzs0Q0FBRUgsU0FBUzs0Q0FBR0UsR0FBRzt3Q0FBRzt3Q0FDN0JlLGFBQWE7NENBQUVqQixTQUFTOzRDQUFHRSxHQUFHO3dDQUFFO3dDQUNoQ2dCLFVBQVU7NENBQUVDLE1BQU07NENBQU1DLFFBQVE7d0NBQVM7d0NBQ3pDZixZQUFZOzRDQUFFQyxVQUFVOzRDQUFLQyxNQUFNO3dDQUFVO2tEQUU3Qyw0RUFBQ25GOzRDQUNDaUcsT0FBTTs0Q0FDTkMsVUFBUzs0Q0FDVGpELFVBQVVvQjs0Q0FDVjFCLFNBQVNsQzs0Q0FDVDBGLGFBQVk7NENBQ1pYLGFBQVk7NENBQ1pZLFNBQVM7Z0RBQ1BDLElBQUk7Z0RBQ0pDLElBQUk7Z0RBQ0pDLElBQUk7Z0RBQ0pDLElBQUk7Z0RBQ0pDLElBQUk7Z0RBQ0osT0FBTzs0Q0FDVDs7Ozs7Ozs7Ozs7a0RBS0osOERBQUMvSCxrREFBTUEsQ0FBQzZGLEdBQUc7d0NBQ1RRLFNBQVM7NENBQUVILFNBQVM7NENBQUc4QixHQUFHLENBQUM7d0NBQUc7d0NBQzlCYixhQUFhOzRDQUFFakIsU0FBUzs0Q0FBRzhCLEdBQUc7d0NBQUU7d0NBQ2hDWixVQUFVOzRDQUFFQyxNQUFNOzRDQUFNQyxRQUFRO3dDQUFTO3dDQUN6Q2YsWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBS0MsTUFBTTs0Q0FBV1MsT0FBTzt3Q0FBSTtrREFFekQsNEVBQUM1Rjs0Q0FDQ2lHLE9BQU07NENBQ05DLFVBQVM7NENBQ1RqRCxVQUFVcUI7NENBQ1YzQixTQUFTOUI7NENBQ1RzRixhQUFZOzRDQUNaWCxhQUFZOzRDQUNaWSxTQUFTO2dEQUNQQyxJQUFJO2dEQUNKQyxJQUFJO2dEQUNKQyxJQUFJO2dEQUNKQyxJQUFJO2dEQUNKQyxJQUFJO2dEQUNKLE9BQU87NENBQ1Q7Ozs7Ozs7Ozs7O2tEQUtKLDhEQUFDL0gsa0RBQU1BLENBQUM2RixHQUFHO3dDQUNUUSxTQUFTOzRDQUFFSCxTQUFTOzRDQUFHQyxPQUFPO3dDQUFJO3dDQUNsQ2dCLGFBQWE7NENBQUVqQixTQUFTOzRDQUFHQyxPQUFPO3dDQUFFO3dDQUNwQ2lCLFVBQVU7NENBQUVDLE1BQU07NENBQU1DLFFBQVE7d0NBQVE7d0NBQ3hDZixZQUFZOzRDQUFFQyxVQUFVOzRDQUFLQyxNQUFNO3dDQUFVO2tEQUU3Qyw0RUFBQ2pGOzRDQUNDK0YsT0FBTTs0Q0FDTkMsVUFBUzs0Q0FDVG5GLFlBQVlBLGNBQWMsRUFBRTs0Q0FDNUJ5RSxhQUFZOzs7Ozs7Ozs7OztvQ0FLZnZELGlCQUFpQjBFLElBQUksQ0FBQ2pFLENBQUFBLE1BQU9BLElBQUlPLFFBQVEsSUFBSVAsSUFBSU8sUUFBUSxDQUFDVixNQUFNLEdBQUcsb0JBQ2xFLDhEQUFDN0Qsa0RBQU1BLENBQUM2RixHQUFHO3dDQUNUUSxTQUFTOzRDQUFFSCxTQUFTOzRDQUFHRSxHQUFHO3dDQUFHO3dDQUM3QmUsYUFBYTs0Q0FBRWpCLFNBQVM7NENBQUdFLEdBQUc7d0NBQUU7d0NBQ2hDZ0IsVUFBVTs0Q0FBRUMsTUFBTTs0Q0FBTUMsUUFBUTt3Q0FBUzt3Q0FDekNmLFlBQVk7NENBQUVDLFVBQVU7NENBQUtDLE1BQU07NENBQVdTLE9BQU87d0NBQUk7a0RBRXpELDRFQUFDM0Y7NENBQ0NnRyxPQUFNOzRDQUNOQyxVQUFTOzRDQUNUbkYsWUFBWUEsY0FBYyxFQUFFOzRDQUM1QmtCLGtCQUFrQkEsaUJBQWlCMkUsTUFBTSxDQUFDbEUsQ0FBQUEsTUFBT0EsSUFBSU8sUUFBUSxJQUFJUCxJQUFJTyxRQUFRLENBQUNWLE1BQU0sR0FBRzs0Q0FDdkZpRCxhQUFZOzs7Ozs7Ozs7OztvQ0FNakJ2RCxpQkFBaUJjLEdBQUcsQ0FBQyxDQUFDOEQsY0FBY0MsZ0JBQ25DLGdEQUFnRDt3Q0FDaER6RSxNQUFNQyxPQUFPLENBQUN1RSxhQUFhNUQsUUFBUSxLQUFLNEQsYUFBYTVELFFBQVEsQ0FBQ1YsTUFBTSxJQUFJLGtCQUN0RSw4REFBQzdELGtEQUFNQSxDQUFDNkYsR0FBRzs0Q0FFVFEsU0FBUztnREFBRUgsU0FBUztnREFBRzhCLEdBQUdJLGdCQUFnQixNQUFNLElBQUksQ0FBQyxLQUFLOzRDQUFHOzRDQUM3RGpCLGFBQWE7Z0RBQUVqQixTQUFTO2dEQUFHOEIsR0FBRzs0Q0FBRTs0Q0FDaENaLFVBQVU7Z0RBQUVDLE1BQU07Z0RBQU1DLFFBQVE7NENBQVE7NENBQ3hDZixZQUFZO2dEQUNWQyxVQUFVO2dEQUNWQyxNQUFNO2dEQUNOUyxPQUFPa0IsZ0JBQWdCOzRDQUN6QjtzREFFQSw0RUFBQzlHO2dEQUNDaUcsT0FBT1ksYUFBYTdELFFBQVEsQ0FBQ1UsSUFBSTtnREFDakNULFVBQVU0RCxhQUFhNUQsUUFBUTtnREFDL0JOLFNBQVNrRSxhQUFhbEUsT0FBTztnREFDN0J3RCxhQUFhLGtCQUE2QyxPQUEzQlUsYUFBYTdELFFBQVEsQ0FBQ0ssSUFBSTtnREFDekRtQyxhQUFhc0IsZ0JBQWdCLE1BQU0sSUFBSSxZQUFZQSxnQkFBZ0IsTUFBTSxJQUFJLGNBQWM7Z0RBQzNGVixTQUFTO29EQUNQQyxJQUFJO29EQUNKQyxJQUFJO29EQUNKQyxJQUFJO29EQUNKQyxJQUFJO29EQUNKQyxJQUFJO29EQUNKLE9BQU87Z0RBQ1Q7Ozs7OzsyQ0F2QkdJLGFBQWE3RCxRQUFRLENBQUMrRCxFQUFFOzs7O3dEQTBCN0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUVgzRiwwQkFBWSw4REFBQ3hCLGtFQUFXQTs7Ozs7Ozs7Ozs7QUFHL0I7R0EzY01POztRQUNjckIsOERBQVFBO1FBRUVNLHFEQUFNQTtRQWtCTlQscURBQVNBO1FBTWRFLHFEQUFTQTtRQU9aRCx3REFBWUE7UUFDUkEsd0RBQVlBO1FBQ1ZBLHdEQUFZQTtRQTBPekJBLHdEQUFZQTtRQVFaQSx3REFBWUE7OztNQXRSckJ1QjtBQTZjTixpRUFBZUEsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IFJlYWN0LCB7IFN1c3BlbnNlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IG1vdGlvbiwgdXNlU2Nyb2xsLCB1c2VUcmFuc2Zvcm0sIHVzZVNwcmluZyB9IGZyb20gXCJmcmFtZXItbW90aW9uXCI7XHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIi4uL2NvbXBvbmVudHMvdWkvdXNlLXRvYXN0XCI7XHJcbmltcG9ydCB7XHJcbiAgQ0FURUdPUklFUyxcclxuICBDQVRFR09SSVpFX1BST0RVQ1RTLFxyXG4gIEZVVFVSRURfUFJPRFVDVFMsXHJcbiAgTUFJTl9VUkwsXHJcbiAgUFJPRFVDVFMsXHJcbn0gZnJvbSBcIi4uL2NvbnN0YW50L3VybHNcIjtcclxuaW1wb3J0IHVzZUFwaSBmcm9tIFwiLi4vaG9va3MvdXNlQXBpXCI7XHJcbmltcG9ydCBNYWluSE9GIGZyb20gXCIuLi9sYXlvdXQvTWFpbkhPRlwiO1xyXG5pbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlRWZmZWN0LCB1c2VTdGF0ZSwgdXNlTWVtbywgdXNlUmVmIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBUcnVzdEluZGljYXRvcnMgZnJvbSBcIkAvY29tcG9uZW50cy91aS9UcnVzdEluZGljYXRvcnNcIjtcclxuaW1wb3J0IFNjcm9sbFRvVG9wIGZyb20gXCJAL2NvbXBvbmVudHMvdWkvU2Nyb2xsVG9Ub3BcIjtcclxuaW1wb3J0IENsaWVudE9ubHkgZnJvbSBcIkAvY29tcG9uZW50cy9DbGllbnRPbmx5XCI7XHJcbmltcG9ydCBcIi4uL3N0eWxlcy9zY3JvbGwtYW5pbWF0aW9ucy5jc3NcIjtcclxuXHJcbi8vIExhenkgbG9hZCBjb21wb25lbnRzIG91dHNpZGUgb2YgdGhlIGNvbXBvbmVudCBmdW5jdGlvblxyXG5jb25zdCBIZXJvQ2Fyb3VzZWwgPSBSZWFjdC5sYXp5KCgpID0+IGltcG9ydCgnQC9jb21wb25lbnRzL2hvbWUvaGVyby1jYXJvdXNlbCcpKTtcclxuY29uc3QgUHJvZHVjdFNlY3Rpb24gPSBSZWFjdC5sYXp5KCgpID0+IGltcG9ydCgnQC9jb21wb25lbnRzL2hvbWUvUHJvZHVjdFNlY3Rpb24nKSk7XHJcbmNvbnN0IENhdGVnb3J5VGFicyA9IFJlYWN0LmxhenkoKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvaG9tZS9DYXRlZ29yeVRhYnMnKSk7XHJcbmNvbnN0IFByb2R1Y3RDYXRlZ29yaWVzID0gUmVhY3QubGF6eSgoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9ob21lL1Byb2R1Y3RDYXRlZ29yaWVzJykpO1xyXG5cclxuLy8gRGVmaW5lIHR5cGVzXHJcbmludGVyZmFjZSBDYXRlZ29yeVByb2R1Y3RzIHtcclxuICBjYXRlZ29yeTogYW55O1xyXG4gIHByb2R1Y3RzOiBhbnlbXTtcclxuICBsb2FkaW5nOiBib29sZWFuO1xyXG59XHJcbmNvbnN0IEhvbWVwYWdlID0gKCkgPT4ge1xyXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KCk7XHJcbiAgLy8gVXNlIGEgc2luZ2xlIEFQSSBpbnN0YW5jZSBmb3IgYWxsIEFQSSBjYWxsc1xyXG4gIGNvbnN0IHsgZGF0YSwgcmVhZCB9OiBhbnkgPSB1c2VBcGkoTUFJTl9VUkwgfHwgJycpO1xyXG5cclxuICAvLyBDcmVhdGUgc3RhdGUgdmFyaWFibGVzIHRvIHN0b3JlIGRpZmZlcmVudCBkYXRhIHR5cGVzXHJcbiAgY29uc3QgW2Z1dHVyZVByb2R1Y3QsIHNldEZ1dHVyZVByb2R1Y3RdID0gdXNlU3RhdGU8YW55PihudWxsKTtcclxuICBjb25zdCBbZnV0dXJlUHJvZHVjdExvYWRpbmcsIHNldEZ1dHVyZVByb2R1Y3RMb2FkaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcclxuICBjb25zdCBbcG9wdWxhclByb2R1Y3QsIHNldFBvcHVsYXJQcm9kdWN0XSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XHJcbiAgY29uc3QgW3BvcHVsYXJQcm9kdWN0TG9hZGluZywgc2V0UG9wdWxhclByb2R1Y3RMb2FkaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcclxuICBjb25zdCBbY2F0ZWdvcmllcywgc2V0Q2F0ZWdvcmllc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xyXG4gIGNvbnN0IFtjYXRlZ29yaWVzTG9hZGluZywgc2V0Q2F0ZWdvcmllc0xvYWRpbmddID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xyXG5cclxuICAvLyBVc2UgcmVmcyB0byB0cmFjayBpZiBkYXRhIGhhcyBiZWVuIGZldGNoZWRcclxuICBjb25zdCBpbml0aWFsRGF0YUZldGNoZWRSZWYgPSB1c2VSZWY8Ym9vbGVhbj4oZmFsc2UpO1xyXG5cclxuICAvLyBDbGllbnQtc2lkZSBvbmx5IHN0YXRlIGZvciBhbmltYXRpb25zXHJcbiAgY29uc3QgW2lzQ2xpZW50LCBzZXRJc0NsaWVudF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIFNjcm9sbCBhbmltYXRpb24gc2V0dXAgLSBvbmx5IG9uIGNsaWVudFxyXG4gIGNvbnN0IGNvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcbiAgY29uc3QgeyBzY3JvbGxZUHJvZ3Jlc3MgfSA9IHVzZVNjcm9sbCh7XHJcbiAgICB0YXJnZXQ6IGNvbnRhaW5lclJlZixcclxuICAgIG9mZnNldDogW1wic3RhcnQgc3RhcnRcIiwgXCJlbmQgc3RhcnRcIl1cclxuICB9KTtcclxuXHJcbiAgLy8gU21vb3RoIHNwcmluZyBhbmltYXRpb24gZm9yIHNjcm9sbCBwcm9ncmVzc1xyXG4gIGNvbnN0IHNtb290aFByb2dyZXNzID0gdXNlU3ByaW5nKHNjcm9sbFlQcm9ncmVzcywge1xyXG4gICAgc3RpZmZuZXNzOiAxMDAsXHJcbiAgICBkYW1waW5nOiAzMCxcclxuICAgIHJlc3REZWx0YTogMC4wMDFcclxuICB9KTtcclxuXHJcbiAgLy8gUGFyYWxsYXggdHJhbnNmb3JtcyBmb3IgYmFja2dyb3VuZCBlbGVtZW50c1xyXG4gIGNvbnN0IGJhY2tncm91bmRZID0gdXNlVHJhbnNmb3JtKHNtb290aFByb2dyZXNzLCBbMCwgMV0sIFtcIjAlXCIsIFwiNTAlXCJdKTtcclxuICBjb25zdCBiYWNrZ3JvdW5kU2NhbGUgPSB1c2VUcmFuc2Zvcm0oc21vb3RoUHJvZ3Jlc3MsIFswLCAxXSwgWzEsIDEuMV0pO1xyXG4gIGNvbnN0IGJhY2tncm91bmRPcGFjaXR5ID0gdXNlVHJhbnNmb3JtKHNtb290aFByb2dyZXNzLCBbMCwgMC41LCAxXSwgWzAuNywgMC40LCAwLjFdKTtcclxuXHJcbiAgLy8gU2V0IGNsaWVudC1zaWRlIGZsYWcgYWZ0ZXIgaHlkcmF0aW9uXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldElzQ2xpZW50KHRydWUpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgW2NhdGVnb3J5UHJvZHVjdHMsIHNldENhdGVnb3J5UHJvZHVjdHNdID0gdXNlU3RhdGU8Q2F0ZWdvcnlQcm9kdWN0c1tdPihbXSk7XHJcblxyXG4gIC8vIFRyYWNrIGlmIGNhdGVnb3J5IHByb2R1Y3RzIGhhdmUgYmVlbiBmZXRjaGVkXHJcbiAgY29uc3QgY2F0ZWdvcnlQcm9kdWN0c0ZldGNoZWRSZWYgPSB1c2VSZWY8Ym9vbGVhbj4oZmFsc2UpO1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byBmZXRjaCBwcm9kdWN0cyBmb3IgZWFjaCBjYXRlZ29yeSAtIG9wdGltaXplZCB0byBwcmV2ZW50IGNvbnRpbnVvdXMgQVBJIGNhbGxzXHJcbiAgY29uc3QgZmV0Y2hDYXRlZ29yeVByb2R1Y3RzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xyXG4gICAgLy8gU2tpcCBpZiBjYXRlZ29yaWVzIGFyZW4ndCBsb2FkZWQgeWV0XHJcbiAgICBpZiAoIUFycmF5LmlzQXJyYXkoY2F0ZWdvcmllcykgfHwgY2F0ZWdvcmllcy5sZW5ndGggPT09IDApIHJldHVybjtcclxuXHJcbiAgICAvLyBTa2lwIGlmIHdlJ3ZlIGFscmVhZHkgZmV0Y2hlZCBhbmQgaGF2ZSBkYXRhXHJcbiAgICBpZiAoY2F0ZWdvcnlQcm9kdWN0c0ZldGNoZWRSZWYuY3VycmVudCAmJlxyXG4gICAgICAgIGNhdGVnb3J5UHJvZHVjdHMubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgIGNhdGVnb3J5UHJvZHVjdHMuZXZlcnkoY2F0ID0+ICFjYXQubG9hZGluZykpIHtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIExpbWl0IHRvIGZpcnN0IDYgY2F0ZWdvcmllcyB0byBhdm9pZCBvdmVyd2hlbG1pbmcgdGhlIHBhZ2UgYW5kIHJlZHVjZSBBUEkgY2FsbHNcclxuICAgIGNvbnN0IGxpbWl0ZWRDYXRlZ29yaWVzID0gY2F0ZWdvcmllcy5zbGljZSgwLCA2KTtcclxuXHJcbiAgICAvLyBTZXQgaW5pdGlhbCBsb2FkaW5nIHN0YXRlXHJcbiAgICBpZiAoY2F0ZWdvcnlQcm9kdWN0cy5sZW5ndGggPT09IDApIHtcclxuICAgICAgY29uc3QgaW5pdGlhbENhdGVnb3J5UHJvZHVjdHMgPSBsaW1pdGVkQ2F0ZWdvcmllcy5tYXAoKGNhdGVnb3J5OiBhbnkpID0+ICh7XHJcbiAgICAgICAgY2F0ZWdvcnksXHJcbiAgICAgICAgcHJvZHVjdHM6IFtdLFxyXG4gICAgICAgIGxvYWRpbmc6IHRydWUsXHJcbiAgICAgIH0pKTtcclxuICAgICAgc2V0Q2F0ZWdvcnlQcm9kdWN0cyhpbml0aWFsQ2F0ZWdvcnlQcm9kdWN0cyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRmV0Y2ggcHJvZHVjdHMgZm9yIGVhY2ggY2F0ZWdvcnlcclxuICAgIGNvbnN0IHByb21pc2VzID0gbGltaXRlZENhdGVnb3JpZXMubWFwKGFzeW5jIChjYXRlZ29yeSwgaW5kZXgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICAvLyBVc2UgdGhlIHNpbmdsZSByZWFkIGZ1bmN0aW9uIGZyb20gb3VyIGNvbnNvbGlkYXRlZCBBUEkgaW5zdGFuY2VcclxuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZWFkKFxyXG4gICAgICAgICAgYCR7Q0FURUdPUklaRV9QUk9EVUNUUyhjYXRlZ29yeS5zbHVnKX0/cGFnZV9zaXplPThgXHJcbiAgICAgICAgKTtcclxuXHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgIGluZGV4LFxyXG4gICAgICAgICAgY2F0ZWdvcnksXHJcbiAgICAgICAgICBwcm9kdWN0czogcmVzdWx0Py5yZXN1bHRzPy5wcm9kdWN0cyB8fCBbXSxcclxuICAgICAgICAgIHN1Y2Nlc3M6IHRydWVcclxuICAgICAgICB9O1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGZldGNoaW5nIHByb2R1Y3RzIGZvciAke2NhdGVnb3J5Lm5hbWV9OmAsIGVycm9yKTtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgaW5kZXgsXHJcbiAgICAgICAgICBjYXRlZ29yeSxcclxuICAgICAgICAgIHByb2R1Y3RzOiBbXSxcclxuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlXHJcbiAgICAgICAgfTtcclxuICAgICAgfVxyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gV2FpdCBmb3IgYWxsIHByb21pc2VzIHRvIHJlc29sdmVcclxuICAgIGNvbnN0IHJlc3VsdHMgPSBhd2FpdCBQcm9taXNlLmFsbChwcm9taXNlcyk7XHJcblxyXG4gICAgLy8gVXBkYXRlIHN0YXRlIG9uY2Ugd2l0aCBhbGwgcmVzdWx0c1xyXG4gICAgc2V0Q2F0ZWdvcnlQcm9kdWN0cyhwcmV2ID0+IHtcclxuICAgICAgLy8gU3RhcnQgd2l0aCBwcmV2aW91cyBzdGF0ZSBvciBlbXB0eSBhcnJheVxyXG4gICAgICBjb25zdCBuZXdTdGF0ZSA9IHByZXYubGVuZ3RoID4gMCA/IFsuLi5wcmV2XSA6XHJcbiAgICAgICAgbGltaXRlZENhdGVnb3JpZXMubWFwKGNhdCA9PiAoeyBjYXRlZ29yeTogY2F0LCBwcm9kdWN0czogW10sIGxvYWRpbmc6IHRydWUgfSkpO1xyXG5cclxuICAgICAgLy8gVXBkYXRlIHdpdGggbmV3IHJlc3VsdHNcclxuICAgICAgcmVzdWx0cy5mb3JFYWNoKHJlc3VsdCA9PiB7XHJcbiAgICAgICAgaWYgKG5ld1N0YXRlW3Jlc3VsdC5pbmRleF0pIHtcclxuICAgICAgICAgIG5ld1N0YXRlW3Jlc3VsdC5pbmRleF0gPSB7XHJcbiAgICAgICAgICAgIC4uLm5ld1N0YXRlW3Jlc3VsdC5pbmRleF0sXHJcbiAgICAgICAgICAgIHByb2R1Y3RzOiByZXN1bHQucHJvZHVjdHMsXHJcbiAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLFxyXG4gICAgICAgICAgfTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gTWFyayBhcyBmZXRjaGVkXHJcbiAgICAgIGNhdGVnb3J5UHJvZHVjdHNGZXRjaGVkUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG5cclxuICAgICAgcmV0dXJuIG5ld1N0YXRlO1xyXG4gICAgfSk7XHJcbiAgfSwgW2NhdGVnb3JpZXMsIHJlYWRdKTtcclxuXHJcbiAgLy8gTG9hZCBhbGwgaW5pdGlhbCBkYXRhIHdpdGggYSBzaW5nbGUgdXNlRWZmZWN0IHRvIHJlZHVjZSByZW5kZXJzIGFuZCBwcmV2ZW50IGNvbnRpbnVvdXMgQVBJIGNhbGxzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFNraXAgaWYgd2UndmUgYWxyZWFkeSBsb2FkZWQgdGhlIGRhdGFcclxuICAgIGlmIChpbml0aWFsRGF0YUZldGNoZWRSZWYuY3VycmVudCkgcmV0dXJuO1xyXG5cclxuICAgIC8vIENyZWF0ZSBhIGZsYWcgdG8gdHJhY2sgaWYgdGhlIGNvbXBvbmVudCBpcyBzdGlsbCBtb3VudGVkXHJcbiAgICBsZXQgaXNNb3VudGVkID0gdHJ1ZTtcclxuXHJcbiAgICBjb25zdCBsb2FkSW5pdGlhbERhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgc2V0Q2F0ZWdvcmllc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICAgICAgc2V0RnV0dXJlUHJvZHVjdExvYWRpbmcodHJ1ZSk7XHJcbiAgICAgICAgc2V0UG9wdWxhclByb2R1Y3RMb2FkaW5nKHRydWUpO1xyXG5cclxuICAgICAgICAvLyBMb2FkIGNhdGVnb3JpZXMgZmlyc3RcclxuICAgICAgICBjb25zdCBjYXRlZ29yaWVzUmVzdWx0ID0gYXdhaXQgcmVhZChDQVRFR09SSUVTKTtcclxuXHJcbiAgICAgICAgLy8gT25seSBjb250aW51ZSBpZiBjb21wb25lbnQgaXMgc3RpbGwgbW91bnRlZFxyXG4gICAgICAgIGlmICghaXNNb3VudGVkKSByZXR1cm47XHJcblxyXG4gICAgICAgIC8vIFVwZGF0ZSBjYXRlZ29yaWVzIHN0YXRlXHJcbiAgICAgICAgaWYgKGNhdGVnb3JpZXNSZXN1bHQpIHtcclxuICAgICAgICAgIHNldENhdGVnb3JpZXMoY2F0ZWdvcmllc1Jlc3VsdCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzZXRDYXRlZ29yaWVzTG9hZGluZyhmYWxzZSk7XHJcblxyXG4gICAgICAgIC8vIExvYWQgZmVhdHVyZWQgYW5kIHBvcHVsYXIgcHJvZHVjdHMgaW4gcGFyYWxsZWxcclxuICAgICAgICBjb25zdCBbZmVhdHVyZWRSZXN1bHQsIHBvcHVsYXJSZXN1bHRdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xyXG4gICAgICAgICAgcmVhZChGVVRVUkVEX1BST0RVQ1RTKSxcclxuICAgICAgICAgIHJlYWQoUFJPRFVDVFMgKyAnP3BhZ2Vfc2l6ZT0xMCcpIC8vIEZldGNoIDEwIHByb2R1Y3RzIGZvciB0aGUgRGlzY292ZXIgc2VjdGlvblxyXG4gICAgICAgIF0pO1xyXG5cclxuICAgICAgICBpZiAoIWlzTW91bnRlZCkgcmV0dXJuO1xyXG5cclxuICAgICAgICAvLyBVcGRhdGUgZmVhdHVyZWQgcHJvZHVjdHMgc3RhdGVcclxuICAgICAgICBpZiAoZmVhdHVyZWRSZXN1bHQpIHtcclxuICAgICAgICAgIHNldEZ1dHVyZVByb2R1Y3QoZmVhdHVyZWRSZXN1bHQpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gVXBkYXRlIHBvcHVsYXIgcHJvZHVjdHMgc3RhdGVcclxuICAgICAgICBpZiAocG9wdWxhclJlc3VsdCkge1xyXG4gICAgICAgICAgc2V0UG9wdWxhclByb2R1Y3QocG9wdWxhclJlc3VsdCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBNYXJrIGFzIGZldGNoZWQgdG8gcHJldmVudCBkdXBsaWNhdGUgQVBJIGNhbGxzXHJcbiAgICAgICAgaW5pdGlhbERhdGFGZXRjaGVkUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG4gICAgICAgIHNldEZ1dHVyZVByb2R1Y3RMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgICBzZXRQb3B1bGFyUHJvZHVjdExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBsb2FkaW5nIGluaXRpYWwgZGF0YTpcIiwgZXJyb3IpO1xyXG4gICAgICAgIHNldENhdGVnb3JpZXNMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgICBzZXRGdXR1cmVQcm9kdWN0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgc2V0UG9wdWxhclByb2R1Y3RMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBsb2FkSW5pdGlhbERhdGEoKTtcclxuXHJcbiAgICAvLyBDbGVhbnVwIGZ1bmN0aW9uIHRvIHByZXZlbnQgc3RhdGUgdXBkYXRlcyBhZnRlciB1bm1vdW50XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpc01vdW50ZWQgPSBmYWxzZTtcclxuICAgIH07XHJcbiAgfSwgW3JlYWRdKTtcclxuXHJcbiAgLy8gRmV0Y2ggcHJvZHVjdHMgZm9yIGVhY2ggY2F0ZWdvcnkgd2hlbiBjYXRlZ29yaWVzIGFyZSBsb2FkZWRcclxuICAvLyBUaGlzIHVzZUVmZmVjdCBub3cgZGVwZW5kcyBvbmx5IG9uIGNhdGVnb3JpZXMgYW5kIGZldGNoQ2F0ZWdvcnlQcm9kdWN0c1xyXG4gIC8vIEl0IHdpbGwgb25seSBydW4gd2hlbiBjYXRlZ29yaWVzIGNoYW5nZSwgbm90IG9uIGV2ZXJ5IHJlbmRlclxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoY2F0ZWdvcmllcyAmJiBjYXRlZ29yaWVzLmxlbmd0aCA+IDAgJiYgIWNhdGVnb3J5UHJvZHVjdHNGZXRjaGVkUmVmLmN1cnJlbnQpIHtcclxuICAgICAgZmV0Y2hDYXRlZ29yeVByb2R1Y3RzKCk7XHJcbiAgICB9XHJcbiAgfSwgW2ZldGNoQ2F0ZWdvcnlQcm9kdWN0cywgY2F0ZWdvcmllc10pO1xyXG5cclxuXHJcbiAgLy8gV2Ugbm8gbG9uZ2VyIG5lZWQgZmVhdHVyZWRQcm9kdWN0c0Zvckhlcm8gc2luY2Ugd2UncmUgdXNpbmcgSGVyb0Nhcm91c2VsXHJcblxyXG4gIC8vIEdldCBmZWF0dXJlZCBwcm9kdWN0cyBmb3IgcHJvZHVjdCBzZWN0aW9uIC0gbWVtb2l6ZWQgdG8gcHJldmVudCByZWNhbGN1bGF0aW9uc1xyXG4gIGNvbnN0IGZlYXR1cmVkUHJvZHVjdHMgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIGlmIChmdXR1cmVQcm9kdWN0ICYmIEFycmF5LmlzQXJyYXkoZnV0dXJlUHJvZHVjdCkpIHtcclxuICAgICAgcmV0dXJuIGZ1dHVyZVByb2R1Y3Q7XHJcbiAgICB9IGVsc2UgaWYgKGZ1dHVyZVByb2R1Y3QgJiYgZnV0dXJlUHJvZHVjdC5yZXN1bHRzICYmIEFycmF5LmlzQXJyYXkoZnV0dXJlUHJvZHVjdC5yZXN1bHRzKSkge1xyXG4gICAgICByZXR1cm4gZnV0dXJlUHJvZHVjdC5yZXN1bHRzO1xyXG4gICAgfSBlbHNlIGlmIChmdXR1cmVQcm9kdWN0ICYmIGZ1dHVyZVByb2R1Y3QucHJvZHVjdHMgJiYgQXJyYXkuaXNBcnJheShmdXR1cmVQcm9kdWN0LnByb2R1Y3RzKSkge1xyXG4gICAgICByZXR1cm4gZnV0dXJlUHJvZHVjdC5wcm9kdWN0cztcclxuICAgIH1cclxuICAgIC8vIFJldHVybiBlbXB0eSBhcnJheSBpZiBubyBwcm9kdWN0cyBhcmUgYXZhaWxhYmxlIC0gbm8gbW9yZSBzdGF0aWMgZmFsbGJhY2tcclxuICAgIHJldHVybiBbXTtcclxuICB9LCBbZnV0dXJlUHJvZHVjdF0pO1xyXG5cclxuICAvLyBHZXQgcG9wdWxhciBwcm9kdWN0cyAtIG1lbW9pemVkIHRvIHByZXZlbnQgcmVjYWxjdWxhdGlvbnNcclxuICBjb25zdCBwb3B1bGFyUHJvZHVjdHMgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIC8vIEZpcnN0IGNoZWNrIG91ciBkZWRpY2F0ZWQgcG9wdWxhclByb2R1Y3Qgc3RhdGVcclxuICAgIGlmIChwb3B1bGFyUHJvZHVjdCAmJiBBcnJheS5pc0FycmF5KHBvcHVsYXJQcm9kdWN0KSkge1xyXG4gICAgICByZXR1cm4gcG9wdWxhclByb2R1Y3Q7XHJcbiAgICB9IGVsc2UgaWYgKHBvcHVsYXJQcm9kdWN0ICYmIHBvcHVsYXJQcm9kdWN0LnJlc3VsdHMgJiYgQXJyYXkuaXNBcnJheShwb3B1bGFyUHJvZHVjdC5yZXN1bHRzKSkge1xyXG4gICAgICByZXR1cm4gcG9wdWxhclByb2R1Y3QucmVzdWx0cztcclxuICAgIH0gZWxzZSBpZiAocG9wdWxhclByb2R1Y3QgJiYgcG9wdWxhclByb2R1Y3QucHJvZHVjdHMgJiYgQXJyYXkuaXNBcnJheShwb3B1bGFyUHJvZHVjdC5wcm9kdWN0cykpIHtcclxuICAgICAgcmV0dXJuIHBvcHVsYXJQcm9kdWN0LnByb2R1Y3RzO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEZhbGxiYWNrIHRvIGRhdGEgZnJvbSB0aGUgbWFpbiBBUEkgY2FsbCBpZiBwb3B1bGFyUHJvZHVjdCBpc24ndCBhdmFpbGFibGVcclxuICAgIGlmIChkYXRhICYmIEFycmF5LmlzQXJyYXkoZGF0YSkpIHtcclxuICAgICAgcmV0dXJuIGRhdGE7XHJcbiAgICB9IGVsc2UgaWYgKGRhdGEgJiYgZGF0YS5yZXN1bHRzICYmIEFycmF5LmlzQXJyYXkoZGF0YS5yZXN1bHRzKSkge1xyXG4gICAgICByZXR1cm4gZGF0YS5yZXN1bHRzO1xyXG4gICAgfSBlbHNlIGlmIChkYXRhICYmIGRhdGEucHJvZHVjdHMgJiYgQXJyYXkuaXNBcnJheShkYXRhLnByb2R1Y3RzKSkge1xyXG4gICAgICByZXR1cm4gZGF0YS5wcm9kdWN0cztcclxuICAgIH1cclxuXHJcbiAgICAvLyBSZXR1cm4gZW1wdHkgYXJyYXkgaWYgbm8gcHJvZHVjdHMgYXJlIGF2YWlsYWJsZSAtIG5vIG1vcmUgc3RhdGljIGZhbGxiYWNrXHJcbiAgICByZXR1cm4gW107XHJcbiAgfSwgW3BvcHVsYXJQcm9kdWN0LCBkYXRhXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IHJlZj17Y29udGFpbmVyUmVmfSBjbGFzc05hbWU9XCJiZy10aGVtZS1ob21lcGFnZSB3LWZ1bGwgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuIHNjcm9sbC1zbmFwLWNvbnRhaW5lclwiPlxyXG4gICAgICB7LyogU2Nyb2xsIFByb2dyZXNzIEluZGljYXRvciAtIENsaWVudCBTaWRlIE9ubHkgKi99XHJcbiAgICAgIDxDbGllbnRPbmx5PlxyXG4gICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCB0b3AtMCBsZWZ0LTAgcmlnaHQtMCBoLTEgYmctZ3JhZGllbnQtdG8tciBmcm9tLXRoZW1lLWFjY2VudC1wcmltYXJ5IHZpYS10aGVtZS1hY2NlbnQtc2Vjb25kYXJ5IHRvLXRoZW1lLWFjY2VudC1wcmltYXJ5IHotNTAgb3JpZ2luLWxlZnRcIlxyXG4gICAgICAgICAgc3R5bGU9e3sgc2NhbGVYOiBzbW9vdGhQcm9ncmVzcyB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvQ2xpZW50T25seT5cclxuXHJcbiAgICAgIHsvKiBFbmhhbmNlZCBCYWNrZ3JvdW5kIGRlY29yYXRpdmUgZWxlbWVudHMgd2l0aCBwYXJhbGxheCAtIENsaWVudCBTaWRlIE9ubHkgKi99XHJcbiAgICAgIHtpc0NsaWVudCAmJiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBsZWZ0LTAgdy1mdWxsIGgtZnVsbCBvdmVyZmxvdy1oaWRkZW4gcG9pbnRlci1ldmVudHMtbm9uZVwiPlxyXG4gICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBsZWZ0LTAgdy1mdWxsIGgtWzgwMHB4XSBiZy1ncmFkaWVudC10by1iIGZyb20tdGhlbWUtYWNjZW50LXByaW1hcnkvNSB0by10cmFuc3BhcmVudFwiXHJcbiAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICBvcGFjaXR5OiBiYWNrZ3JvdW5kT3BhY2l0eSxcclxuICAgICAgICAgICAgc2NhbGU6IGJhY2tncm91bmRTY2FsZVxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAvPlxyXG4gICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtWzEwJV0gcmlnaHQtWy0xMCVdIHctWzUwMHB4XSBoLVs1MDBweF0gcm91bmRlZC1mdWxsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tdGhlbWUtYWNjZW50LXNlY29uZGFyeS8xMCB0by10aGVtZS1hY2NlbnQtcHJpbWFyeS81IGJsdXItM3hsXCJcclxuICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgIHk6IGJhY2tncm91bmRZLFxyXG4gICAgICAgICAgICBzY2FsZTogYmFja2dyb3VuZFNjYWxlLFxyXG4gICAgICAgICAgICBvcGFjaXR5OiBiYWNrZ3JvdW5kT3BhY2l0eVxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAvPlxyXG4gICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtWzQwJV0gbGVmdC1bLTEwJV0gdy1bNjAwcHhdIGgtWzYwMHB4XSByb3VuZGVkLWZ1bGwgYmctZ3JhZGllbnQtdG8tdHIgZnJvbS10aGVtZS1hY2NlbnQtcHJpbWFyeS81IHRvLXRoZW1lLWFjY2VudC1zZWNvbmRhcnkvMTAgYmx1ci0zeGxcIlxyXG4gICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgeTogdXNlVHJhbnNmb3JtKHNtb290aFByb2dyZXNzLCBbMCwgMV0sIFtcIjAlXCIsIFwiLTMwJVwiXSksXHJcbiAgICAgICAgICAgIHNjYWxlOiBiYWNrZ3JvdW5kU2NhbGUsXHJcbiAgICAgICAgICAgIG9wYWNpdHk6IGJhY2tncm91bmRPcGFjaXR5XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS1bNSVdIHJpZ2h0LVs1JV0gdy1bNDAwcHhdIGgtWzQwMHB4XSByb3VuZGVkLWZ1bGwgYmctZ3JhZGllbnQtdG8tYmwgZnJvbS10aGVtZS1hY2NlbnQtc2Vjb25kYXJ5LzEwIHRvLXRoZW1lLWFjY2VudC1wcmltYXJ5LzUgYmx1ci0zeGxcIlxyXG4gICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgeTogdXNlVHJhbnNmb3JtKHNtb290aFByb2dyZXNzLCBbMCwgMV0sIFtcIjAlXCIsIFwiMjAlXCJdKSxcclxuICAgICAgICAgICAgc2NhbGU6IGJhY2tncm91bmRTY2FsZSxcclxuICAgICAgICAgICAgb3BhY2l0eTogYmFja2dyb3VuZE9wYWNpdHlcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHsvKiA8UHJvbW9CYW5uZXIgLz4gKi99XHJcbiAgICAgIDxNYWluSE9GPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggZmxleC1jb2wgdy1mdWxsIHJlbGF0aXZlIHotMTAgc2Nyb2xsLXNuYXAtY29udGFpbmVyXCI+XHJcbiAgICAgICAgICB7LyogTmF2aWdhdGlvbiAtIEFtYXpvbi9GbGlwa2FydCBzdHlsZSBjYXRlZ29yeSBuYXZpZ2F0aW9uICovfVxyXG4gICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0yMFwiXHJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogLTIwIH19XHJcbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGVhc2U6IFwiZWFzZU91dFwiIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxQcm9kdWN0Q2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgIGNhdGVnb3JpZXM9e2NhdGVnb3JpZXMgPz8gW119XHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm5hdmlnYXRpb25cIlxyXG4gICAgICAgICAgICAgIHNob3dUaXRsZT17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgc2hvd1ZpZXdBbGw9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgIG1heENhdGVnb3JpZXM9ezEyfVxyXG4gICAgICAgICAgICAgIGFjY2VudENvbG9yPVwicHJpbWFyeVwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcblxyXG4gICAgICAgICAgey8qIFVzZSBhIHNpbmdsZSBTdXNwZW5zZSBib3VuZGFyeSBmb3IgYWxsIGxhenktbG9hZGVkIGNvbXBvbmVudHMgKi99XHJcbiAgICAgICAgICA8Q2xpZW50T25seT5cclxuICAgICAgICAgICAgPFJlYWN0LlN1c3BlbnNlIGZhbGxiYWNrPXtcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBzcGFjZS15LThcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtWzQwMHB4XSBiZy1ncmF5LTEwMCBhbmltYXRlLXB1bHNlIHJvdW5kZWQteGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtWzMwMHB4XSBiZy1ncmF5LTEwMCBhbmltYXRlLXB1bHNlIHJvdW5kZWQteGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtWzMwMHB4XSBiZy1ncmF5LTEwMCBhbmltYXRlLXB1bHNlIHJvdW5kZWQteGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgfT5cclxuICAgICAgICAgICAgICB7LyogSGVybyBTZWN0aW9uIHdpdGggRmVhdHVyZWQgUHJvZHVjdHMgKi99XHJcbiAgICAgICAgICAgICAgPG1vdGlvbi5zZWN0aW9uXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGwgc2Nyb2xsLXNuYXAtc2VjdGlvblwiXHJcbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHNjYWxlOiAwLjk1IH19XHJcbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XHJcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGVhc2U6IFwiZWFzZU91dFwiLCBkZWxheTogMC4yIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPEhlcm9DYXJvdXNlbCAvPlxyXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBtdC02IG1iLThcIlxyXG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XHJcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGVhc2U6IFwiZWFzZU91dFwiLCBkZWxheTogMC41IH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxUcnVzdEluZGljYXRvcnMgLz5cclxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgICAgICAgICA8L21vdGlvbi5zZWN0aW9uPlxyXG5cclxuICAgICAgICAgICAgey8qIEZlYXR1cmVkIFByb2R1Y3RzIFNlY3Rpb24gKi99XHJcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2Nyb2xsLXNuYXAtc2VjdGlvblwiXHJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiA2MCB9fVxyXG4gICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cclxuICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlLCBtYXJnaW46IFwiLTEwMHB4XCIgfX1cclxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGVhc2U6IFwiZWFzZU91dFwiIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8UHJvZHVjdFNlY3Rpb25cclxuICAgICAgICAgICAgICAgIHRpdGxlPVwiRmVhdHVyZWQgUHJvZHVjdHNcIlxyXG4gICAgICAgICAgICAgICAgc3VidGl0bGU9XCJEaXNjb3ZlciBvdXIgaGFuZHBpY2tlZCBzZWxlY3Rpb24gb2YgcHJlbWl1bSBwcm9kdWN0c1wiXHJcbiAgICAgICAgICAgICAgICBwcm9kdWN0cz17ZmVhdHVyZWRQcm9kdWN0c31cclxuICAgICAgICAgICAgICAgIGxvYWRpbmc9e2Z1dHVyZVByb2R1Y3RMb2FkaW5nfVxyXG4gICAgICAgICAgICAgICAgdmlld0FsbExpbms9XCIvc2hvcFwiXHJcbiAgICAgICAgICAgICAgICBhY2NlbnRDb2xvcj1cInByaW1hcnlcIlxyXG4gICAgICAgICAgICAgICAgY29sdW1ucz17e1xyXG4gICAgICAgICAgICAgICAgICB4czogMixcclxuICAgICAgICAgICAgICAgICAgc206IDIsXHJcbiAgICAgICAgICAgICAgICAgIG1kOiAzLFxyXG4gICAgICAgICAgICAgICAgICBsZzogNCxcclxuICAgICAgICAgICAgICAgICAgeGw6IDQsXHJcbiAgICAgICAgICAgICAgICAgIFwiMnhsXCI6IDVcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIFBvcHVsYXIgUHJvZHVjdHMgU2VjdGlvbiAqL31cclxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC02MCB9fVxyXG4gICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cclxuICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlLCBtYXJnaW46IFwiLTEwMHB4XCIgfX1cclxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGVhc2U6IFwiZWFzZU91dFwiLCBkZWxheTogMC4yIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8UHJvZHVjdFNlY3Rpb25cclxuICAgICAgICAgICAgICAgIHRpdGxlPVwiRGlzY292ZXIgUHJvZHVjdHNcIlxyXG4gICAgICAgICAgICAgICAgc3VidGl0bGU9XCJFeHBsb3JlIG91ciBtb3N0IHBvcHVsYXIgaXRlbXNcIlxyXG4gICAgICAgICAgICAgICAgcHJvZHVjdHM9e3BvcHVsYXJQcm9kdWN0c31cclxuICAgICAgICAgICAgICAgIGxvYWRpbmc9e3BvcHVsYXJQcm9kdWN0TG9hZGluZ31cclxuICAgICAgICAgICAgICAgIHZpZXdBbGxMaW5rPVwiL3Nob3BcIlxyXG4gICAgICAgICAgICAgICAgYWNjZW50Q29sb3I9XCJzZWNvbmRhcnlcIlxyXG4gICAgICAgICAgICAgICAgY29sdW1ucz17e1xyXG4gICAgICAgICAgICAgICAgICB4czogMixcclxuICAgICAgICAgICAgICAgICAgc206IDIsXHJcbiAgICAgICAgICAgICAgICAgIG1kOiAzLFxyXG4gICAgICAgICAgICAgICAgICBsZzogNCxcclxuICAgICAgICAgICAgICAgICAgeGw6IDQsXHJcbiAgICAgICAgICAgICAgICAgIFwiMnhsXCI6IDVcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIFByb2R1Y3QgQ2F0ZWdvcmllcyBHcmlkIC0gQWx3YXlzIHJlbmRlciB3aXRoIGZhbGxiYWNrIGNhdGVnb3JpZXMgKi99XHJcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC45IH19XHJcbiAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cclxuICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlLCBtYXJnaW46IFwiLTUwcHhcIiB9fVxyXG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNywgZWFzZTogXCJlYXNlT3V0XCIgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxQcm9kdWN0Q2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJTaG9wIGJ5IENhdGVnb3J5XCJcclxuICAgICAgICAgICAgICAgIHN1YnRpdGxlPVwiQnJvd3NlIG91ciBjb2xsZWN0aW9uIGJ5IGNhdGVnb3J5XCJcclxuICAgICAgICAgICAgICAgIGNhdGVnb3JpZXM9e2NhdGVnb3JpZXMgfHwgW119XHJcbiAgICAgICAgICAgICAgICBhY2NlbnRDb2xvcj1cInRlcnRpYXJ5XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogQ2F0ZWdvcnkgVGFicyBTZWN0aW9uIC0gT25seSByZW5kZXIgY2F0ZWdvcmllcyB3aXRoIHByb2R1Y3RzICovfVxyXG4gICAgICAgICAgICB7Y2F0ZWdvcnlQcm9kdWN0cy5zb21lKGNhdCA9PiBjYXQucHJvZHVjdHMgJiYgY2F0LnByb2R1Y3RzLmxlbmd0aCA+IDApICYmIChcclxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiA4MCB9fVxyXG4gICAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSwgbWFyZ2luOiBcIi0xMDBweFwiIH19XHJcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjksIGVhc2U6IFwiZWFzZU91dFwiLCBkZWxheTogMC4xIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPENhdGVnb3J5VGFic1xyXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkJyb3dzZSBQcm9kdWN0cyBieSBDYXRlZ29yeVwiXHJcbiAgICAgICAgICAgICAgICAgIHN1YnRpdGxlPVwiRmlsdGVyIHByb2R1Y3RzIGJ5IHlvdXIgZmF2b3JpdGUgY2F0ZWdvcmllc1wiXHJcbiAgICAgICAgICAgICAgICAgIGNhdGVnb3JpZXM9e2NhdGVnb3JpZXMgfHwgW119XHJcbiAgICAgICAgICAgICAgICAgIGNhdGVnb3J5UHJvZHVjdHM9e2NhdGVnb3J5UHJvZHVjdHMuZmlsdGVyKGNhdCA9PiBjYXQucHJvZHVjdHMgJiYgY2F0LnByb2R1Y3RzLmxlbmd0aCA+IDApfVxyXG4gICAgICAgICAgICAgICAgICBhY2NlbnRDb2xvcj1cInByaW1hcnlcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICB7LyogSW5kaXZpZHVhbCBDYXRlZ29yeSBTZWN0aW9ucyAqL31cclxuICAgICAgICAgICAge2NhdGVnb3J5UHJvZHVjdHMubWFwKChjYXRlZ29yeURhdGEsIGNhdGVnb3J5SW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAvLyBPbmx5IHNob3cgY2F0ZWdvcmllcyB3aXRoIGF0IGxlYXN0IDQgcHJvZHVjdHNcclxuICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KGNhdGVnb3J5RGF0YS5wcm9kdWN0cykgJiYgY2F0ZWdvcnlEYXRhLnByb2R1Y3RzLmxlbmd0aCA+PSA0ID8gKFxyXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgICAga2V5PXtjYXRlZ29yeURhdGEuY2F0ZWdvcnkuaWR9XHJcbiAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogY2F0ZWdvcnlJbmRleCAlIDIgPT09IDAgPyAtNjAgOiA2MCB9fVxyXG4gICAgICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB4OiAwIH19XHJcbiAgICAgICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUsIG1hcmdpbjogXCItODBweFwiIH19XHJcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3tcclxuICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMC44LFxyXG4gICAgICAgICAgICAgICAgICAgIGVhc2U6IFwiZWFzZU91dFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGRlbGF5OiBjYXRlZ29yeUluZGV4ICogMC4xXHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxQcm9kdWN0U2VjdGlvblxyXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPXtjYXRlZ29yeURhdGEuY2F0ZWdvcnkubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICBwcm9kdWN0cz17Y2F0ZWdvcnlEYXRhLnByb2R1Y3RzfVxyXG4gICAgICAgICAgICAgICAgICAgIGxvYWRpbmc9e2NhdGVnb3J5RGF0YS5sb2FkaW5nfVxyXG4gICAgICAgICAgICAgICAgICAgIHZpZXdBbGxMaW5rPXtgL3Nob3A/Y2F0ZWdvcnk9JHtjYXRlZ29yeURhdGEuY2F0ZWdvcnkuc2x1Z31gfVxyXG4gICAgICAgICAgICAgICAgICAgIGFjY2VudENvbG9yPXtjYXRlZ29yeUluZGV4ICUgMyA9PT0gMCA/IFwicHJpbWFyeVwiIDogY2F0ZWdvcnlJbmRleCAlIDMgPT09IDEgPyBcInNlY29uZGFyeVwiIDogXCJ0ZXJ0aWFyeVwifVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgIHhzOiAyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgc206IDIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBtZDogMyxcclxuICAgICAgICAgICAgICAgICAgICAgIGxnOiA0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgeGw6IDQsXHJcbiAgICAgICAgICAgICAgICAgICAgICBcIjJ4bFwiOiA1XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgICAgICAgICApIDogbnVsbFxyXG4gICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9SZWFjdC5TdXNwZW5zZT5cclxuICAgICAgICAgIDwvQ2xpZW50T25seT5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9NYWluSE9GPlxyXG5cclxuICAgICAgey8qIFNjcm9sbCB0byBUb3AgQnV0dG9uIC0gQ2xpZW50IFNpZGUgT25seSAqL31cclxuICAgICAge2lzQ2xpZW50ICYmIDxTY3JvbGxUb1RvcCAvPn1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBIb21lcGFnZTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwibW90aW9uIiwidXNlU2Nyb2xsIiwidXNlVHJhbnNmb3JtIiwidXNlU3ByaW5nIiwidXNlVG9hc3QiLCJDQVRFR09SSUVTIiwiQ0FURUdPUklaRV9QUk9EVUNUUyIsIkZVVFVSRURfUFJPRFVDVFMiLCJNQUlOX1VSTCIsIlBST0RVQ1RTIiwidXNlQXBpIiwiTWFpbkhPRiIsInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VNZW1vIiwidXNlUmVmIiwiVHJ1c3RJbmRpY2F0b3JzIiwiU2Nyb2xsVG9Ub3AiLCJDbGllbnRPbmx5IiwiSGVyb0Nhcm91c2VsIiwibGF6eSIsIlByb2R1Y3RTZWN0aW9uIiwiQ2F0ZWdvcnlUYWJzIiwiUHJvZHVjdENhdGVnb3JpZXMiLCJIb21lcGFnZSIsInRvYXN0IiwiZGF0YSIsInJlYWQiLCJmdXR1cmVQcm9kdWN0Iiwic2V0RnV0dXJlUHJvZHVjdCIsImZ1dHVyZVByb2R1Y3RMb2FkaW5nIiwic2V0RnV0dXJlUHJvZHVjdExvYWRpbmciLCJwb3B1bGFyUHJvZHVjdCIsInNldFBvcHVsYXJQcm9kdWN0IiwicG9wdWxhclByb2R1Y3RMb2FkaW5nIiwic2V0UG9wdWxhclByb2R1Y3RMb2FkaW5nIiwiY2F0ZWdvcmllcyIsInNldENhdGVnb3JpZXMiLCJjYXRlZ29yaWVzTG9hZGluZyIsInNldENhdGVnb3JpZXNMb2FkaW5nIiwiaW5pdGlhbERhdGFGZXRjaGVkUmVmIiwiaXNDbGllbnQiLCJzZXRJc0NsaWVudCIsImNvbnRhaW5lclJlZiIsInNjcm9sbFlQcm9ncmVzcyIsInRhcmdldCIsIm9mZnNldCIsInNtb290aFByb2dyZXNzIiwic3RpZmZuZXNzIiwiZGFtcGluZyIsInJlc3REZWx0YSIsImJhY2tncm91bmRZIiwiYmFja2dyb3VuZFNjYWxlIiwiYmFja2dyb3VuZE9wYWNpdHkiLCJjYXRlZ29yeVByb2R1Y3RzIiwic2V0Q2F0ZWdvcnlQcm9kdWN0cyIsImNhdGVnb3J5UHJvZHVjdHNGZXRjaGVkUmVmIiwiZmV0Y2hDYXRlZ29yeVByb2R1Y3RzIiwiQXJyYXkiLCJpc0FycmF5IiwibGVuZ3RoIiwiY3VycmVudCIsImV2ZXJ5IiwiY2F0IiwibG9hZGluZyIsImxpbWl0ZWRDYXRlZ29yaWVzIiwic2xpY2UiLCJpbml0aWFsQ2F0ZWdvcnlQcm9kdWN0cyIsIm1hcCIsImNhdGVnb3J5IiwicHJvZHVjdHMiLCJwcm9taXNlcyIsImluZGV4IiwicmVzdWx0Iiwic2x1ZyIsInJlc3VsdHMiLCJzdWNjZXNzIiwiZXJyb3IiLCJjb25zb2xlIiwibmFtZSIsIlByb21pc2UiLCJhbGwiLCJwcmV2IiwibmV3U3RhdGUiLCJmb3JFYWNoIiwiaXNNb3VudGVkIiwibG9hZEluaXRpYWxEYXRhIiwiY2F0ZWdvcmllc1Jlc3VsdCIsImZlYXR1cmVkUmVzdWx0IiwicG9wdWxhclJlc3VsdCIsImZlYXR1cmVkUHJvZHVjdHMiLCJwb3B1bGFyUHJvZHVjdHMiLCJkaXYiLCJyZWYiLCJjbGFzc05hbWUiLCJzdHlsZSIsInNjYWxlWCIsIm9wYWNpdHkiLCJzY2FsZSIsInkiLCJpbml0aWFsIiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImVhc2UiLCJ2YXJpYW50Iiwic2hvd1RpdGxlIiwic2hvd1ZpZXdBbGwiLCJtYXhDYXRlZ29yaWVzIiwiYWNjZW50Q29sb3IiLCJTdXNwZW5zZSIsImZhbGxiYWNrIiwic2VjdGlvbiIsImRlbGF5Iiwid2hpbGVJblZpZXciLCJ2aWV3cG9ydCIsIm9uY2UiLCJtYXJnaW4iLCJ0aXRsZSIsInN1YnRpdGxlIiwidmlld0FsbExpbmsiLCJjb2x1bW5zIiwieHMiLCJzbSIsIm1kIiwibGciLCJ4bCIsIngiLCJzb21lIiwiZmlsdGVyIiwiY2F0ZWdvcnlEYXRhIiwiY2F0ZWdvcnlJbmRleCIsImlkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});